<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Order\Components\OrderItemsForm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Delivery\DeliveryInformation;
use App\Model\Orm\Order\Delivery\DeliveryType;
use App\Model\Orm\Order\Delivery\LegacyDeliveryInformation;
use App\Model\Orm\Order\Delivery\PhysicalDeliveryInformation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductType\ProductType;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Orm\Stock\Stock;
use App\PostType\Page\Model\Orm\CatalogTreeModel;
use App\Model\Translator;
use Nette\Application\UI\Form;
use Nette\Forms\Container;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;

final class OrderItemsFormBuilder
{


	private bool $disableEdit = true;

	public function build(Form $form, Order $order): void
	{
		$this->disableEdit = !$order->isActionAllowed(Order::ORDER_ACTION_ITEMS_EDIT);

		$this->addProductVariants($form, $order->getBuyableItems());
		$this->addButtonsToForm($form);
	}

	private function addButtonsToForm(Form $form): void {
		if ($this->disableEdit) {
			return;
		}
		$form->addSubmit('send');
	}

	/**
	 * @param ProductItem[]|IEntity[] $collection
	 */
	private function addProductVariants(Form $form, array $collection): void
	{
		$itemsContainer = $form->addContainer('items');
		foreach ($collection as $item) {
			$itemContainer = $itemsContainer->addContainer($item->id);
			$itemContainer->addHidden('id')
				->setDisabled($this->disableEdit)
				->setDefaultValue($item->id);
			$itemContainer->addText('name')
				->setDisabled($this->disableEdit)
				->setDefaultValue($item->variantName);
			$itemContainer->addText('amount')
				->setDisabled($this->disableEdit)
				->setDefaultValue($item->amount);
			$itemContainer->addText('unitPrice')
				->setDisabled($this->disableEdit)
				->setDefaultValue($item->unitPrice->asMoney()->getAmount()->toFloat());
			$itemContainer->addText('unitPriceVat')
				->setDisabled($this->disableEdit);
		}

		if (!$this->disableEdit) {
			$itemContainer = $itemsContainer->addContainer('newItemMarker');
			$itemContainer->addHidden('id');
			$itemContainer->addText('name');
			$itemContainer->addText('amount');
			$itemContainer->addText('unitPrice');
			$itemContainer->addText('unitPriceVat');
		}
	}

}
