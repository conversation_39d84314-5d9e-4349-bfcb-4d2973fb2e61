<?php

declare(strict_types=1);

namespace App\Model\ShoppingCart;

use App\Event\AddPaymentInfo;
use App\Event\AddShippingInfo;
use App\Event\RemoveFromCart;
use App\Exceptions\LogicException;
use App\Model\ConfigService;
use App\Model\Consent\MarketingConsent;
use App\Model\DeliveryDate;
use App\Model\Erp\ErpOrderService;
use App\Model\Messenger\Erp\Order\OrderExportMessage;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Class\ClassItem;
use App\Model\Orm\Order\Delivery\OrderDelivery;
use App\Model\Orm\Order\Gift\GiftItem;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderModel;
use App\Model\Orm\Order\OrderProxy;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Order\Payment\OrderPayment;
use App\Model\Orm\Order\PickupPoint\PickupPoint;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Order\Promotion\PromotionItem;
use App\Model\Orm\Order\RefreshResult;
use App\Model\Orm\Order\Sync\OrderSyncType;
use App\Model\Orm\Order\Voucher\VoucherItem;
use App\Model\Orm\Orm;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\State\State;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\User\User as UserEntity;
use App\Model\Orm\User\UserModel;
use App\Model\Orm\User\UserProvider;
use App\Model\Orm\VoucherCode\VoucherCode;
use App\Model\ShoppingCart\Handlers\UserAuthenticationHandler;
use App\Model\ShoppingCart\Storage\CookieStorageFactory;
use App\Model\ShoppingCart\Storage\Storage;
use App\Model\StaticPage\StaticPage;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use App\PostType\Page\Model\Orm\Tree;
use App\Utils\DateTime;
use Brick\Math\BigDecimal;
use Brick\Money\Currency;
use Brick\Money\Money;
use Contributte\Events\Extra\Event\Security\LoggedInEvent;
use Contributte\Events\Extra\Event\Security\LoggedOutEvent;
use Jaybizzle\CrawlerDetect\CrawlerDetect;
use Nette\Http\Request;
use Nette\Http\Response;
use Nette\Security\User;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;
use Tracy\Debugger;
use Tracy\Logger;

final class ShoppingCart implements EventSubscriberInterface, ShoppingCartInterface
{
	use HasStaticCache {
		HasStaticCache::flushCache as staticCacheFlush;
	}
	private ?Order $order = null;

	private ?Currency $currency = null;

	private ?Mutation $mutation = null;

	private Storage $storage;

	public function __construct(
		private readonly UserProvider $userProvider,
		private readonly Orm $orm,
		CookieStorageFactory $storageFactory,
		private readonly UserAuthenticationHandler $userAuthenticationHandler,
		private readonly OrderModel $orderModel,
		private readonly MarketingConsent $marketingConsent,
		private readonly Request $httpRequest,
		private readonly Response $httpResponse,
		private readonly EventDispatcherInterface $eventDispatcher,
		private readonly ConfigService $configService,
		private readonly ErpOrderService $erpOrderService,
		private readonly MessageBusInterface $messageBus,

	)
	{
		$this->storage = $storageFactory->create();
	}

	private function init(): void
	{
		if ($this->mutation === null) {
			throw new LogicException('Mutation is not set.');
		}
		$this->order = $this->getOrder();
	}

	public function getZeroPrice(): Money
	{
		return Price::from(Money::of(0, $this->getCurrency()))->asMoney();
	}

	public function isEmpty(): bool
	{
		if ($this->order === null) {
			return true;
		}

		return $this->order->isEmpty();
	}

	public function getTotalPrice(bool $withVat = false, bool $withDelivery = false, bool $includeGiftCertificates = true, bool $withPromotions = true, bool $withFreeDeliveryVoucher = true, ?int $precision = null): Money
	{
		if ($this->order === null) {
			return $this->getZeroPrice();
		}

		return $this->order->getTotalPrice($withVat,$withDelivery,$includeGiftCertificates, $withPromotions, $withFreeDeliveryVoucher, precision: $precision);
	}

	public function getTotalPriceWithDelivery(?int $precision = null): Money
	{
		if ($this->order === null) {
			return $this->getZeroPrice();
		}

		return $this->order->getTotalPriceWithDelivery($precision);
	}

	public function getTotalPriceVat(
		bool $withDelivery = false,
		bool $includeGiftCertificates = true,
		bool $withFreeDeliveryVoucher = true,
		?int $precision = null
	): Money
	{
		if ($this->order === null) {
			return $this->getZeroPrice();
		}

		return $this->order->getTotalPriceVat(withDelivery: $withDelivery, includeGiftCertificates: $includeGiftCertificates, withFreeDeliveryVoucher: $withFreeDeliveryVoucher, precision: $precision);
	}

	public function getTotalGiftCertificatesPrice(bool $withVat = true): Money
	{
		if ($this->order === null) {
			return $this->getZeroPrice();
		}
		return $this->order->getTotalGiftCertificatesPrice($withVat);
	}

	public function getTotalPriceWithDeliveryVat(?int $precision = null): Money
	{
		if ($this->order === null) {
			return $this->getZeroPrice();
		}

		return $this->order->getTotalPriceWithDeliveryVat(precision: $precision);
	}

	public function getTotalWeight():BigDecimal
	{
		if ($this->order === null) {
			return BigDecimal::zero();
		}
		return $this->order->getTotalWeight();
	}

	public function getTotalCount(bool $onlyProducts = true): int
	{
		if ($this->order === null) {
			return 0;
		}

		return $this->order->getTotalCount($onlyProducts);
	}

	public function getTotalProducts(): int
	{
		if ($this->order === null) {
			return 0;
		}
		return $this->order->products->count();
	}

	public function getTotalClassEvents(): int
	{
		if ($this->order === null) {
			return 0;
		}
		return $this->order->classEvents->count();
	}

	public function getTotalProductPrice(bool $withVat = false, ?array $productIds = null): Money
	{
		if ($this->order === null) {
			return $this->getZeroPrice();
		}
		return $this->order->getTotalProductPrice($withVat,$productIds);
	}

	public function getTotalProductPriceVat(): Money
	{
		if ($this->order === null) {
			return $this->getZeroPrice();
		}

		return $this->order->getTotalProductPriceVat();
	}

	public function getTotalDiscountVat(): Money
	{
		$discount = Money::of(0, $this->getCurrency());
		if ($this->order === null) {
			return $discount;
		}
		return $this->order->getTotalDiscountVat();
	}

	public function getTotalOriginalPriceVat(): Money
	{
		if ($this->order === null) {
			return $this->getZeroPrice();
		}

		return $this->order->getTotalOriginalPriceVat();
	}

	public function hasGiftVouchers(): bool
	{
		if ($this->order === null) {
			return false;
		}

		return $this->order->hasGiftVouchers();
	}
	/**
	 * @return ICollection<VoucherItem>
	 */
	public function getVouchers(): ICollection
	{
		if ($this->order === null) {
			/** @var EmptyCollection<VoucherItem> $emptyCollection */
			$emptyCollection = new EmptyCollection();
			return $emptyCollection;
		}

		return $this->order->getVouchers();
	}

	public function getAppliedVoucherItem(): ?VoucherItem
	{
		return $this->order?->getAppliedVoucherItem();
	}

	/**
	 * @return ICollection<PromotionItem>
	 */
	public function getPromotions(): ICollection
	{
		if ($this->order === null) {
			/** @var EmptyCollection<PromotionItem> $emptyCollection */
			$emptyCollection = new EmptyCollection();
			return $emptyCollection;
		}
		return $this->order->getPromotions();
	}

	/**
	 * @return ICollection<GiftItem>
	 */
	public function getGifts(): ICollection
	{
		if ($this->order === null) {
			/** @var EmptyCollection<GiftItem> $emptyCollection */
			$emptyCollection = new EmptyCollection();
			return $emptyCollection;
		}

		return $this->order->getGifts();
	}

	/**
	 * @return ICollection<ProductItem>
	 */
	public function getProducts(): ICollection
	{
		if ($this->order === null) {
			/** @var EmptyCollection<ProductItem> $emptyCollection */
			$emptyCollection = new EmptyCollection();
			return $emptyCollection;
		}
		return $this->order->getProducts();
	}

	public function getClassEvents():ICollection
	{
		if ($this->order === null) {
			/** @var EmptyCollection<ClassItem> $emptyCollection */
			$emptyCollection = new EmptyCollection();
			return $emptyCollection;
		}
		return $this->order->getClassEvents();
	}

	public function getProductsIds(): array
	{
		if ($this->order === null) {
			return [];
		}
		return $this->order->getProductsIds();
	}

	public function hasProductId(int|array $productId): bool
	{
		if ($this->order === null) {
			return false;
		}
		return $this->order->hasProductId($productId);
	}

	public function containsProductId(int|array $productId): bool
	{
		if ($this->order === null) {
			return false;
		}
		return $this->order->containsProductId($productId);
	}

	public function getItems(): array
	{
		if ($this->order === null) {
			return [];
		}
		return $this->order->getItems();
	}

	public function getBuyableItems(): array
	{
		if ($this->order === null) {
			return [];
		}
		return $this->order->getBuyableItems();
	}

	public function getAmounts(): array
	{
		$amounts = [];
		/** @var ProductItem $productItem */
		foreach ($this->getProducts() as $productItem) {
			$amounts['products-' . $productItem->variant->id] = $productItem->amount;
		}
		/** @var ClassItem $classItem */
		foreach ($this->getClassEvents() as $classItem) {
			$amounts['classEvents-' . $classItem->getIdentifier()] = $classItem->amount;
		}

		return $amounts;
	}

	public function addProduct(ProductVariant $variant, int $amount = 1): int
	{
		$this->initOrder();
		$addedAmount = $this->order->addProduct($variant, $amount);
		$this->orm->persistAndFlush($this->order);
		return $addedAmount;

	}

	public function removeProduct(ProductVariant $variant): ?ProductItem
	{
		$this->initOrder();
		$entity = $this->order->removeProduct($variant);
		if ($entity !== null) {
			$this->eventDispatcher->dispatch(
				new RemoveFromCart(
					shoppingCart: $this,
					productLocalization: $variant->product->getLocalization($this->order->mutation),
					variant: $variant,
					mutation: $this->order->mutation,
					state: $this->order->country,
					priceLevel: $this->order->priceLevel,
					currency: $this->getCurrency(),
					quantity: $entity->amount,
				)
			);
			$this->orm->removeAndFlush($entity);
		}
		return null;
	}

	public function subtractProduct(ProductVariant $variant, int $amount = 1): null|int|ProductItem
	{
		$this->initOrder();
		$newAmount = $this->order->subtractProduct($variant, $amount);
		if (is_int($newAmount)) {
			$this->orm->persistAndFlush($this->order);
		} elseif ($newAmount instanceof ProductItem) {
			$this->eventDispatcher->dispatch(
				new RemoveFromCart(
					shoppingCart: $this,
					productLocalization: $variant->product->getLocalization($this->order->mutation),
					variant: $variant,
					mutation: $this->order->mutation,
					state: $this->order->country,
					priceLevel: $this->order->priceLevel,
					currency: $this->getCurrency(),
					quantity: $newAmount->amount,
				)
			);
			$this->orm->removeAndFlush($newAmount);
		}
		return null;
	}

	public function addClass(Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel, int $amount = 1): int
	{
		$this->initOrder();
		$addedAmount = $this->order->addClass($product, $classEvent, $priceLevel, amount: $amount);
		$this->orm->persistAndFlush($this->order);
		return $addedAmount;

	}

	public function removeClass(Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel): ?ClassItem
	{
		$this->initOrder();
		$entity = $this->order->removeClass($product, $classEvent, $priceLevel);
		if ($entity !== null) {
			$this->eventDispatcher->dispatch(
				new RemoveFromCart(
					shoppingCart: $this,
					productLocalization: $product->getLocalization($this->order->mutation),
					variant: $product->firstVariant,
					mutation: $this->order->mutation,
					state: $this->order->country,
					priceLevel: $this->order->priceLevel,
					currency: $this->getCurrency(),
					quantity: $entity->amount,
				)
			);
			$this->orm->removeAndFlush($entity);
		}
		return null;
	}

	public function subtractClass(Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel, int $amount = 1): null|int|ClassItem
	{
		$this->initOrder();
		$newAmount = $this->order->subtractClass($product, $classEvent, $priceLevel, $amount);
		if (is_int($newAmount)) {
			$this->orm->persistAndFlush($this->order);
		} elseif ($newAmount instanceof ClassItem) {
			$this->eventDispatcher->dispatch(
				new RemoveFromCart(
					shoppingCart: $this,
					productLocalization: $product->getLocalization($this->order->mutation),
					variant: $product->firstVariant,
					mutation: $this->order->mutation,
					state: $this->order->country,
					priceLevel: $this->order->priceLevel,
					currency: $this->getCurrency(),
					quantity: $newAmount->amount,
				)
			);
			$this->orm->removeAndFlush($newAmount);
		}
		return null;
	}

	public function addVoucher(VoucherCode $voucher): string|true
	{
		$this->initOrder();
		$result = $this->order->addVoucher($voucher);
		$this->orm->persistAndFlush($this->order);
		return $result;
	}

	public function removeVoucher(VoucherCode $voucher): ?VoucherItem
	{
		$this->initOrder();
		$entity = $this->order->removeVoucher($voucher);
		if ($entity !== null) {
			$this->orm->removeAndFlush($entity);
		}
		return null;
	}

	/**
	 * @param VoucherCode[] $exclude
	 */
	public function flushVouchers(array $exclude = [], bool $withGiftCertificates = false): array
	{
		$this->initOrder();
		$removed = $this->order->flushVouchers($exclude, $withGiftCertificates);
		foreach ($removed as $entity) {
			$this->orm->remove($entity);
		}
		$this->orm->flush();
		return $removed;
	}

	public function addGift(GiftLocalization $giftLocalization): bool
	{
		$this->initOrder();
		$result = $this->order->addGift($giftLocalization);
		$this->orm->persistAndFlush($this->order);
		return $result;
	}

	public function removeGift(GiftLocalization $giftLocalization): ?GiftItem
	{
		$this->initOrder();
		$entity = $this->order->removeGift($giftLocalization);
		if ($entity !== null) {
			$this->orm->removeAndFlush($entity);
		}
		return null;
	}

	public function flushGifts(): array
	{
		$this->initOrder();
		$removed = $this->order->flushGifts();
		foreach ($removed as $entity) {
			$this->orm->remove($entity);
		}
		$this->orm->flush();
		return [];
	}

	public function hasGift(): bool
	{
		if ($this->order === null) {
			return false;
		}
		return $this->order->hasGift();
	}

	public function hasFreeDeliveryVoucher(): bool
	{
		if ($this->order === null) {
			return false;
		}
		return $this->order->hasFreeDeliveryVoucher();
	}

	public function hasItemWithForcedFreeDelivery(): bool
	{
		if ($this->order === null) {
			return false;
		}
		return $this->order->hasItemWithForcedFreeDelivery();
	}

	public function setDelivery(OrderDelivery|DeliveryMethodConfiguration|null $item, bool $ignoreState = false): void
	{
		assert($item instanceof DeliveryMethodConfiguration || $item === null);
		$this->initOrder();
		$this->orderModel->setDelivery($this->order, $item);
		if ($item !== null) {
			$this->eventDispatcher->dispatch(new AddShippingInfo($this));
		}
		$this->orm->flush();
	}

	public function setPickupPoint(?int $pickupPointId): void
	{
		$this->initOrder();
		$this->orderModel->setPickupPoint($this->order, $pickupPointId);
		$this->orm->flush();
	}

	public function getPickupPointId(): string|null
	{
		$this->initOrder();
		return $this->order->getPickupPointId();
	}

	public function getPickupPoint(): ?PickupPoint
	{
		$this->initOrder();

		if ($this->order->getPickupPointId() === null) {
			return null;
		}

		return $this->orm->pickupPoint->getById($this->order->getPickupPointId());
	}

	public function getDelivery(): ?OrderDelivery
	{
		$this->initOrder();
		return $this->order->getDelivery();
	}

	public function setPayment(PaymentMethodConfiguration|OrderPayment|null $payment): void
	{
		assert($payment instanceof PaymentMethodConfiguration || $payment === null);
		$this->initOrder();
		$this->orderModel->setPayment($this->order, $payment);
		if ($payment !== null) {
			$this->eventDispatcher->dispatch(new AddPaymentInfo($this));
		}
		$this->orm->flush();
	}

	public function getPayment(): ?OrderPayment
	{
		$this->initOrder();
		return $this->order->getPayment();
	}


	public function refresh(): array
	{
		if ($this->order === null) {
			return [];
		}

		$changedItems = $this->order->refresh();
		foreach ($changedItems as $changedItem) {
			if ($changedItem->removed) {
				$this->orm->removeAndFlush($changedItem->item);
			} elseif ($changedItem->updated) {
				$this->orm->persistAndFlush($changedItem->item);
			}
		}

		if ($changedItems !== []) {
			$this->orm->persistAndFlush($this->order);
		}

		return $changedItems;
	}

	/**
	 * @param bool $onlyProducts
	 * @param bool $onlyToRemove
	 *
	 * @return RefreshResult[]
	 */
	public function getChangedItems(bool $onlyProducts = false, bool $onlyToRemove = false): array
	{
		if ($this->order === null) {
			return [];
		}

		$changed = $this->order->refresh(withDelete: false, onlyProducts: $onlyProducts);
		if (!$onlyToRemove) {
			return $changed;
		}

		return array_filter($changed, function ($changedItem) {
			return $changedItem->removed;
		});
	}

	public function hasDelivery(): bool
	{
		if ($this->order === null) {
			return false;
		}
		return $this->order->hasDelivery();
	}

	public function hasPayment(): bool
	{
		if ($this->order === null) {
			return false;
		}
		return $this->order->hasPayment();
	}

	public function placeOrder(ArrayHash $orderDetails, ArrayHash $orderDeliveryDetails): array
	{
		$this->initOrder();

		try {
			$changes = $this->orderModel->place($this->order, $orderDetails, $orderDeliveryDetails);

			// if something changed, persist it and return
			if ($changes !== []) {
				$this->orm->persistAndFlush($this->order);
				return $changes;
			}

			// if everything is ok, flush
			$this->orm->flush();

		} catch (Throwable $e) {
			// set state to default (Draft)
			$this->order->state = OrderState::Draft;

			// Log error
			Debugger::log($e, Logger::EXCEPTION);

			// show error for developer
			bdump($e);

			// Return RefreshResult with message
			return [ RefreshResult::of(null, 'an_error_occurred') ];
		}

		//send order to erp
		if ($this->configService->isEnvProduction()) {
			$this->messageBus->dispatch(new OrderExportMessage((int)$this->order->novikoIdObjednavkaPart, OrderSyncType::OrderCreateManual));
		} else {
			$this->erpOrderService->createOrder($this->order, OrderSyncType::OrderCreateManual);
		}

		return [];
	}


	public function getOrderHash(): ?string
	{
		return $this->order?->hash;
	}

	public function getOrderId(): ?int
	{
		return $this->order?->id;
	}


	private function initOrder(): void
	{
		// Crawler and other bots detection
		$crawlerDetect = new CrawlerDetect();
		if ($crawlerDetect->isCrawler()) {
			return;
		}

		// Otherwise, if order not exists, create it
		if ($this->order === null) {
			$this->order = $this->createOrder();
		}

		// if user is logged in and has shopping cart id
		if ($this->order->user === null && $this->getUserEntity() !== null) {
			$this->order->user = $this->getUserEntity();
			$this->orm->persistAndFlush($this->order);
		}
	}

	public function initAutoItems(?Order $order = null): array
	{
		if ($this->order === null) {
			return [];
		}

		$changed = [];
		if ($this->order->getTotalCount() && ($changed = $this->order->initAutoItems()) !== []) {
			/** @var RefreshResult $changedItem */
			foreach ($changed as $changedItem) {
				if ($changedItem->removed) {
					$this->orm->removeAndFlush($changedItem->item);
				} elseif ($changedItem->updated) {
					$this->orm->persistAndFlush($changedItem->item);
				}
			}
		}
		return $changed;
	}

	private function getOrder(): ?Order
	{
		if(($orderCookieHash = $this->storage->get()) !== null) {
			$order = $this->orm->order->getBy(['cookieHash' => $orderCookieHash, 'state' => OrderState::Draft, 'mutation' => $this->getMutation()]);
			if ($order !== null && $order->state === OrderState::Draft) {
				return $order;
			}
		}

		$userEntity = $this->userProvider->userEntity;
		if ($userEntity !== null) {
			$order = $this->orm->order->getBy([
				'state' => OrderState::Draft,
				'user' => $userEntity,
				'mutation' => $this->getMutation(),
			]);

			if ($order !== null) {
				$this->storage->set($order->cookieHash);
				return $order;
			}
		}

		return null;
	}

	public function hasOrder(): bool
	{
		return $this->order !== null;
	}

	public function getUserEntity(): ?UserEntity
	{
		if ($this->order === null || $this->order->user === null) {
			if ($this->userProvider->userSecurity->isLoggedIn()) {
				return $this->userProvider->userEntity;
			}
		}
		return $this->order?->user;
	}

	private function createOrder(): Order
	{
		$userEntity = $this->userProvider->userEntity;
		$mutation = $this->getMutation();

		$rememberedCountry = null;
		$rememberedCountryId = $this->httpRequest->getCookie('shoppingCartCountryId');
		if($rememberedCountryId !== null && in_array((int) $rememberedCountryId, $this->getMutation()->states->toCollection()->fetchPairs(null, 'id'))) {
			$rememberedCountry = $this->orm->state->getById($rememberedCountryId);
		}

		$order = new Order(
			$mutation,
			$userEntity?->priceLevel ?? $this->orm->priceLevel->getDefault(),
			$rememberedCountry ?? $userEntity?->state ?? $this->orm->state->getDefault($mutation),
			$userEntity,
			$this->currency ?? $mutation->currency,
		);

		$this->orm->persistAndFlush($order);
		$this->storage->set($order->cookieHash);

		return $order;
	}

	public function onLoggedIn(): void
	{
		$this->order = $this->getOrder();
		$this->userAuthenticationHandler->handleLoggedIn($this, $this->storage, $this->order);
	}

	public function onLoggedOut(): void
	{
		$this->userAuthenticationHandler->handleLoggedOut($this, $this->storage, $this->order);
	}

	public static function getSubscribedEvents(): array
	{
		return [
			LoggedInEvent::class => 'onLoggedIn',
			LoggedOutEvent::class => 'onLoggedOut',
		];
	}

	public function setCurrency(string $currencyCode): void
	{
		// compatibility with other projects, in SA not used
		if (class_exists(\App\Model\Currency\CurrencyHelper::class)) {
			if (!in_array($currencyCode, \App\Model\Currency\CurrencyHelper::CURRENCIES)) {
				return;
			}

			$this->currency = Currency::of($currencyCode);

			if ($this->order !== null && $this->order->currency->getCurrencyCode() !== $currencyCode) {
				$this->order->currency = $this->currency;
				$this->order->setDelivery(null);
				$this->order->setPayment(null);
				$this->flushGifts();
				$this->flushVouchers([], true);
				$this->order->refresh();
				$this->orm->persistAndFlush($this->order);
			}
		}
	}

	public function getCurrency(): Currency
	{
		return $this->currency ?? $this->getMutation()->getSelectedCurrency();
	}

	public function getCountry(): ?State
	{
		if ($this->order === null) {
			return null;
		}

		return $this->order->getCountry();
	}

	public function setCountry(State $state): void
	{
		$this->httpResponse->setCookie('shoppingCartCountryId', (string) $state->id, '+1 year');
		$this->initOrder();

		$this->order->country = $state;

		$this->orm->persistAndFlush($this->order);
	}

	public function hasExtendedDelivery(int $days = 10): bool
	{
		return $this->tryLoadCache($this->createCacheKey('extendedDelivery', ... func_get_args()), function () use ($days) {
			$dates = [];
			foreach ($this->getProducts() as $orderItem) {
				if ($orderItem instanceof ProductItem) {
					$deliveryDate = $orderItem->variant->productAvailability->getDeliveryDate($this->order->mutation,	$this->order->country, $this->order->priceLevel, $this->order->currency, quantityRequired: $orderItem->amount);
					if ($deliveryDate !== null) {
						$dates[$deliveryDate->from->format("Y-m-d H:i:s")] = $deliveryDate->from->getTimestamp();
					}
				}
			}

			foreach ($this->getGifts() as $orderItem) {
				if ($orderItem instanceof GiftItem && ($product = $orderItem->giftLocalization->gift->product) !== null) {
					$deliveryDate = $product->firstVariant->productAvailability->getDeliveryDate($this->order->mutation,	$this->order->country, $this->order->priceLevel, $this->order->currency, quantityRequired: $orderItem->amount);
					if ($deliveryDate !== null) {
						$dates[$deliveryDate->from->format("Y-m-d H:i:s")] = $deliveryDate->from->getTimestamp();
					}
				}
			}

			if ($dates !== []) {
				$maxDate = DateTime::from(max($dates));
				$diff    = $maxDate->diff(new DateTime());

				if ($diff->days > $days) {
					return true;
				}
			}

			return false;
		});
	}

	public function hasExtendedStock(int $days = 10): bool
	{
		return $this->tryLoadCache($this->createCacheKey('extendedStock', ... func_get_args()), function () use ($days) {
			$dates = [];
			foreach ($this->getProducts() as $orderItem) {
				if ($orderItem instanceof ProductItem) {
					$stockDate = $orderItem->variant->productAvailability->getStockDate();
					if ($stockDate !== null) {
						$dates[$stockDate->format("Y-m-d H:i:s")] = $stockDate->getTimestamp();
					}
				}
			}

			if ($dates !== []) {
				$maxDate = DateTime::from(max($dates));
				$diff    = $maxDate->diff(new DateTime());

				if ($diff->days > $days) {
					return true;
				}
			}

			return false;
		});
	}

	public function getDefaultWorstDeliveryDate(): null|false|DeliveryDate
	{
		$deliveryDateFinal = false;

		foreach ($this->getProducts() as $orderItem) {
			if ($orderItem instanceof ProductItem) {
				$deliveryDate = $orderItem->variant->productAvailability->getDeliveryDate($this->order->mutation, $this->order->country, $this->order->priceLevel, $this->order->currency, quantityRequired: $orderItem->amount);
				if ($deliveryDate === null) { // product is not available = the total delivery time is on request
					$deliveryDateFinal = null;
					break;
				} elseif ($deliveryDateFinal === false || $deliveryDate->from > $deliveryDateFinal->from) {
					$deliveryDateFinal = $deliveryDate;
				}
			}
		}

		foreach ($this->getGifts() as $orderItem) {
			if ($orderItem instanceof GiftItem && ($product = $orderItem->giftLocalization->gift->product) !== null) {
				$deliveryDate = $product->firstVariant->productAvailability->getDeliveryDate($this->order->mutation, $this->order->country, $this->order->priceLevel, $this->order->currency, quantityRequired: $orderItem->amount);
				if ($deliveryDate === null) { // product is not available = the total delivery time is on request
					$deliveryDateFinal = null;
					break;
				} elseif ($deliveryDateFinal === false || $deliveryDate->from > $deliveryDateFinal->from) {
					$deliveryDateFinal = $deliveryDate;
				}
			}
		}

		return $deliveryDateFinal;
	}

	public function getWorstDeliveryDate(DeliveryMethodConfiguration $deliveryMethodConfiguration): null|false|DeliveryDate
	{
		return $this->loadCache($this->createCacheKey('worstDeliveryDate-', ... func_get_args()), function () use ($deliveryMethodConfiguration) {
			$deliveryDateFinal = false;

			/** @var ProductItem $orderItem */
			foreach ($this->getProducts() as $orderItem) {
				if ($orderItem instanceof ProductItem) {
					$deliveryDate = $orderItem->variant->productAvailability->getDeliveryDate($this->order->mutation, $this->order->country, $this->order->priceLevel, $this->order->currency, $deliveryMethodConfiguration, $orderItem->amount); //->variant->getDeliveryDate($this->mutation, $this->state, $transport, $basketItem->amount);
					if ($deliveryDate === null) { // product is not available = the total delivery time is on request
						$deliveryDateFinal = null;
						break;
					} elseif ($deliveryDateFinal === false || $deliveryDate->from > $deliveryDateFinal->from) {
						$deliveryDateFinal = $deliveryDate;
					}
				}
			}

			/** @var GiftItem $orderItem */
			foreach ($this->getGifts() as $orderItem) {
				if ($orderItem instanceof GiftItem && ($product = $orderItem->giftLocalization->gift->product) !== null) {
					$deliveryDate = $product->firstVariant->productAvailability->getDeliveryDate($this->order->mutation, $this->order->country, $this->order->priceLevel, $this->order->currency, $deliveryMethodConfiguration, $orderItem->amount); //->variant->getDeliveryDate($this->mutation, $this->state, $transport, $basketItem->amount);
					if ($deliveryDate === null) { // product is not available = the total delivery time is on request
						$deliveryDateFinal = null;
						break;
					} elseif ($deliveryDateFinal === false || $deliveryDate->from > $deliveryDateFinal->from) {
						$deliveryDateFinal = $deliveryDate;
					}
				}
			}

			// sluzby
			/*$serviceWorkDays = [];
			foreach ($this->getItems() as $orderItems) {
				foreach ($orderItems as $orderItem) {
					if ($orderItem->type === OrderItem::TYPE_SERVICE && $orderItem->service->hasDeliveryWorkDay) {
						$serviceWorkDays[] = (int)$orderItem->service->deliveryWorkDay;
					}
				}
			}*/

			/*if (count($serviceWorkDays) > 0 && isset($result->deliveryDate->from) && $result->deliveryDate->from instanceof DateTime) {
				$serviceWorkDays = max($serviceWorkDays);
				if ($serviceWorkDays > 0) {
					$result->deliveryDate->from->addWorkday($serviceWorkDays);
				}
			}*/

			return $deliveryDateFinal;
		});
	}

	public function useDeliveryAddress(): bool
	{
		$this->initOrder();

		//if ($this->hasDelivery()) {
		//	return $this->getDelivery()->deliveryMethod->getDeliveryMethod()->useDeliveryAddress();
		//}

		return true;
	}

	public function storeVisit(): void
	{
		if ($this->marketingConsent->isPersonalizationGranted()) {
			$cookie = $this->httpRequest->getCookie('shoppingCartVisit');
			$today  = (new DateTime())->format('Y-m-d');

			if ($cookie === null) {
				$this->httpResponse->setCookie('shoppingCartVisit', $today, '+1 year');
			} elseif ($cookie !== $today) {
				$this->httpResponse->setCookie('shoppingCartVisit', $today, '+1 year');
			}
		}
	}

	public function removeVisit(): void
	{
		$this->httpResponse->deleteCookie('shoppingCartVisit');
	}

	public function showForgottenCart(RoutableEntity|StaticPage $routableEntity): bool
	{
		if ($routableEntity instanceof StaticPage) {
			return false;
		}
		$lastVisit = DateTime::from($this->httpRequest->getCookie('shoppingCartVisit') ?? 'today midnight');
		$ignoredPages = [Tree::UID_CART, Tree::UID_ORDER_STEP_1, Tree::UID_ORDER_STEP_2, TREE::UID_ORDER_STEP_3];

		if ($routableEntity->getMetadata()->hasProperty('uid') && in_array($routableEntity->getValue('uid'), $ignoredPages)) {
			return false;
		}

		if (
			$this->marketingConsent->isPersonalizationGranted() &&
			$this->getTotalProducts() > 0 &&
			$lastVisit->getTimestamp() < (new DateTime())->modifyClone('-2 day')->getTimestamp()
		) {
			return true;
		}
		return false;
	}

	public function doEmpty(): void
	{
		$this->initOrder();

		/** @var ProductItem $productItem */

		foreach ($this->order->products as $productItem) {
			$this->order->products->remove($productItem);
			$this->orm->remove($productItem);
		}

		$this->setDelivery(null);
		$this->setPayment(null);

		$this->orm->flush();
		$this->removeVisit();
	}

	public function flushCache(?string $key = null): void
	{
		$this->order?->flushCache($key);
		$this->staticCacheFlush($key);
	}

	public function getTotalProductIdsPriceVat(array $productIds): Money
	{
		if ($this->order === null) {
			return $this->getZeroPrice();
		}

		return $this->order->getTotalProductIdsPriceVat($productIds);
	}

	public function getProductItemsDiscountPrice(): Money
	{
		if ($this->order === null) {
			return $this->getZeroPrice();
		}

		return $this->order->getProductItemsDiscountPrice();
	}

	public function getMutation(): Mutation
	{
		if ($this->order === null) {
			return $this->mutation;
		}
		return $this->order->mutation;
	}

	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
		$this->storage->setMutation($mutation);
		$this->init();
	}

	public function getPriceLevel(): PriceLevel
	{
		$this->initOrder();
		return $this->order->priceLevel;
	}

	public function hasElectronicProduct(bool $strict = false): bool
	{
		if ($this->order === null) {
			return false;
		}
		return $this->order->hasElectronicProduct($strict);
	}

	public function getUserSecurity(): User
	{
		return $this->userProvider->userSecurity;
	}

	public function flushStorage(): void
	{
		$this->storage->remove();
		$this->removeVisit();
	}

	public function hasCertificate(bool $strict = false): bool
	{
		if ($this->order === null) {
			return false;
		}
		return $this->order->hasCertificate($strict);
	}

	public function isRegistrationRequired(): bool
	{
		if ($this->order === null) {
			return false;
		}
		return $this->order->isRegistrationRequired();
	}
}
