<?php declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Order;

use App\Model\Erp\ErpOrderService;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class OrderExportConsumer
{

	private Mutation $defaultMutation;

	public function __construct(
		private readonly Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly ErpOrderService $erpOrderService
	)
	{
	}

	private function setup(): void
	{
		$this->orm->reconnect();

		$this->defaultMutation = $this->mutationsHolder->getDefault();

		$this->mutationHolder->setMutation($this->defaultMutation);
		$this->orm->setMutation($this->defaultMutation);
		$this->orm->setPublicOnly(false);
	}

	public function __invoke(OrderExportMessage $orderMessage): void
	{
		$this->setup();

		$order = $this->orm->order->getById($orderMessage->getOrderId());

		if ($order !== null) {
			//$this->orderModel->syncErp($order, $orderMessage->getSyncType());
			$this->erpOrderService->createOrder($order, $orderMessage->getSyncType());
		}
	}

}
