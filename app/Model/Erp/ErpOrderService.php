<?php declare(strict_types = 1);

namespace App\Model\Erp;

use App\Model\Erp\Connector\OrderConnector;
use App\Model\Erp\Enum\ErpOrderStatus;
use App\Model\Erp\Exception\ErpException;
use App\Model\Orm\Order\Order;
use App\Model\Erp\Entity\ErpOrder;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Order\Sync\OrderSyncHistory;
use App\Model\Orm\Order\Sync\OrderSyncType;
use App\Model\Orm\Orm;
use Nextras\Dbal\Utils\DateTimeImmutable;
use stdClass;
use Throwable;
use <PERSON>\Debugger;
use Tracy\ILogger;

final class ErpOrderService
{

	private array $message;

	public function __construct(
		private readonly Orm $orm,
		private readonly OrderConnector $orderConnector,
	)
	{
	}

	public function createOrder(Order $order, OrderSyncType $syncType = OrderSyncType::Unexpected): void
	{
		$order->syncStartedAt = new DateTimeImmutable();
		$this->orm->persistAndFlush($order);
		$history = new OrderSyncHistory();
		$history->createdAt = new DateTimeImmutable();
		$history->orderNumber = $order->orderNumber;
		$history->type = $syncType;

		try {
			$erpOrder = new ErpOrder();
			$erpOrder->setDataFromOrder($order);
			$result = $this->orderConnector->saveOrder($erpOrder);
			$order = $this->processResult($order, $result);

			$data = $erpOrder->getData();
			$history->data = $data;

			bdump($data);
			bdump($result);
			$order->syncedAt = new DateTimeImmutable();

			$this->orm->persistAndFlush($order);

			$history->response = $result;

		} catch (\Throwable $exception) {
			$history->response = ['exception' => $exception];
			Debugger::log($exception, ILogger::EXCEPTION);
		}

		$this->orm->persistAndFlush($history);
	}

	public function updateOrder(Order $order, OrderSyncType $syncType = OrderSyncType::OrderUpdate): void
	{
		$order->syncStartedAt = new DateTimeImmutable();
		$this->orm->persistAndFlush($order);
		$history = new OrderSyncHistory();
		$history->createdAt = new DateTimeImmutable();
		$history->orderNumber = $order->orderNumber;
		$history->type = $syncType;

		try {
			//if ($this->configService->isEnvProduction()) { TODO
				$erpOrder = new ErpOrder();
				$erpOrder->setDataFromOrder($order);

				$history->data = $erpOrder->getData();

				$result = $this->orderConnector->updateOrder($erpOrder);
				$order = $this->processResult($order, $result);
			//}

			$order->syncedAt = new DateTimeImmutable();

			/* TODO MichalK
			 * if ($order->subscriptionOrder instanceof Model\SubscriptionOrder && $order->subscriptionOrder->novikoSubmittedAt === null) {
				$order->subscriptionOrder->novikoSubmittedAt = new DateTimeImmutable();
				$log = new Model\SubscriptionOrderLog();
				$log->message = 'Order exported as open to Noviko';
				$log->type = Model\SubscriptionOrderLog::TYPE_INFO;
				$order->subscriptionOrder->logs->add($log);
			}*/

			/** @var Order $order */
			$order = $this->orm->persistAndFlush($order);

			$history->response = $result;

		} catch (\Throwable $exception) {
			$history->response = ['exception' => $exception];
			Debugger::log($exception, ILogger::EXCEPTION);
			$this->message = [$exception->getMessage(), 'error'];
		}

		$this->orm->persistAndFlush($history);
	}

	public function getOrder(Order $order): ?ErpOrder
	{
		$result = $this->orderConnector->getOrder($order->novikoIdObjednavkaPart);
		if ($result === null) {
			return null;
		}
		$erpOrder = new ErpOrder();
		$erpOrder->setDataFromResult($result);
		return $erpOrder;
	}

	public function getOrderStatus(Order $order): ?int
	{
		$erpOrder = $this->getOrder($order);
		return $erpOrder->getstatus();
	}

	public function syncStatus(Order $order): bool
	{
		$erpOrder = $this->getOrder($order);

		if ($erpOrder === null) {
			$this->message = ['order_noviko_msg_unknown_order', 'error'];
			return false;
		} elseif ($order->novikoStatus === $erpOrder->getStatus()) {
			$this->message = ['order_noviko_msg_update_status_no_changes', 'info'];
			return true;
		} else {
			$this->changeStatus($order, $erpOrder);
			$this->message = ['order_noviko_msg_update_status_changed', 'ok'];
			return true;
		}
	}

	public function changeStatus(Order $order, ErpOrder $erpOrder): Order
	{
		$order->novikoStatus = $erpOrder->getStatus(); // always store the noviko status
		$order->syncedAt = new DateTimeImmutable();

		switch ($erpOrder->getStatus()) {
			case ErpOrderStatus::Storno:
				/*if ($order->hasCreditNote) { // pri uplnem stornu by obj nemala uz mit dobropis
					throw new LogicException(sprintf('Order ID %d already has credit note', $order->id));
				}

				$this->orderModel->storno($order);
				if ($order->subscriptionOrder instanceof Model\SubscriptionOrder) {
					$this->subscriptionModel->cancelSubscriptionOrder($order->subscriptionOrder, Model\SubscriptionOrder::CANCELLING_REASON_NOVIKO_CANCEL);
				}
				$data = $this->creditNoteModel->getDataForLastCreditNote($order, Model\Order::CANCEL_REASON_DEFAULT); // kdyby nahodou obj uz mela nejaky D
				$creditNote = $this->creditNoteModel->create($order, $data);*/
				/*if ($creditNote) { // gopay refundace
					$this->creditNoteModel->doPaymentRefund($order, $creditNote);
				}*/

				/*$this->orderMailService->orderStorno($order, $creditNote);*/

				break;
			case ErpOrderStatus::ForwardedToDelivery:
				$order->state = OrderState::Dispatched;
				break;
			case ErpOrderStatus::ReturnedByCarrier:
				$order = $this->orm->persistAndFlush($order); // must be persisted before sending an email
				//$this->orderMailService->statusChanged($order);

				break;
			case ErpOrderStatus::Delivered:
				//TODO create invoice and send email
				break;
			default:
				// other statuses, just set the status and do nothing more
				$order = $this->orm->persistAndFlush($order);

				break;
		}
		return $order;
	}

	private function processResult(Order $order, ?stdClass $result = null): Order
	{
		if (empty($result) || !isset($result->objednavkaOdb->id)) {
			throw new ErpException('Erp result not ok');
		}

		if ($result->objednavkaOdb->idObjednavkaPart !== $order->novikoIdObjednavkaPart) {
			throw new ErpException('idObjednavkaPart doesnt match id in the WS result');
		}

		if (!$order->extId) {
			// first sync save identifiers and status
			$order->extId = empty($result->objednavkaOdb->id) ? null : (int) $result->objednavkaOdb->id;
			$order->novikoHdId = empty($result->objednavkaOdb->idHDObj) ? null : (int) $result->objednavkaOdb->idHDObj;
			$order->novikoStatus = isset($result->status) ? (int) $result->status : null;
		}

		return $order;
	}

	/**
	 * Prida k objednavce cisla baliku z Novika
	 */
	private function handleParcelNumber(Order $order, bool $forcePersist = false): void
	{
		/* todo doplnit načtení čísel balíků
		$data = $this->orderConnector->getOrder($order->novikoIdObjednavkaPart, WsConnector::METHOD_GET_OBJEDNAVKA_ODB);
		if ($data) {
			$list = [];
			$list['id'] = isset($data->id) ? $data->id : NULL;
			$list['idZasilky'] = isset($data->idZasilky) ? $data->idZasilky : NULL;

			if (isset($data->baliky)) {
				$list['baliky'] = is_array($data->baliky) ? $data->baliky : [$data->baliky]; // kdyz je v result jen 1 balik, tak <baliky> neni pole, ale stdClass !

				foreach ($list['baliky'] as &$item) {
					$item = (array)$item;
					if (isset($item['idBalikExt']) && $item['idBalikExt']) { // u Heurekapointu je jen 1 balik <idBalikExt>6032166/1</idBalikExt> = je potreba upravit idBalikExt na 6 mistne cislo
						$ext = explode('/', $item['idBalikExt']);
						$item['idBalikExt'] = isset($ext[0]) ? trim($ext[0]) : '';
					}
				}
			} else {
				$list['baliky'] = [];
			}

			$order->parcelNumber = $list;

			if ($forcePersist) {
				$this->orm->persistAndFlush($order);
			}

			$this->logShort([sprintf('SAVE PARCEL NUMBER Order ID: %d', $order->id)], Logger::INFO, $order->id);

			if ($this->verboseLog) {
				$this->logShort(['PARCEL NUMBER', Tracy\Dumper::toText($list)], Logger::INFO, $order->id);
			}
		}*/
	}

		/**
		 * Synchronizace stavu vsech nevyrizenych objednavek
		 *
		 * Zmeny stavu podle Noviko statusu:
		 * -----------------------------------------
		 *
		 * Noviko                    Eshop
		 * ***********************************
		 * Predano dopravci    ->    Expedovana
		 * Storno                ->    Stornovana
		 *
		 * Aktualizace polozek
		 * -----------------------------------------
		 * jen do Noviko stavu "Ke kompletaci", pak uz by se polozky nemely menit
		 */
	public function updateStatusBulk(): void
	{
		try {
			$this->log(['START', '############', __METHOD__]);

			$orders = $this->orm->order->findBy([
				'extId!=' => null,
				'status!=' => [OrderState::Declined, OrderState::Canceled],
			]);

			if ($orders) {
				$counter = $updated = $skipped = $errored = 0;

				foreach ($orders as $order) {
					//$this->mutationHolder->setMutation($order->mutation);
					if (!isset($order->parcelNumber)) {
						$this->handleParcelNumber($order, true);

						if (!isset($order->parcelNumber)) {
							$this->logShort(
								[sprintf('PARCEL NUMBER is NULL even after attempt to update.  ID %d | ORDER NUMBER: %s | Noviko ID %s. SKIPPED FOR THIS RUN', $order->id, $order->number, $order->novikoId)],
								Logger::INFO,
								$order->id
							);
							continue;
						}
					}

					$originStatus = $order->status;
					$originNovikoStatus = $order->novikoStatus;
					$counter++;
					$erpOrder = $this->getOrder($order);

					if (!$erpOrder) {
						$this->logShort([sprintf('UNKNOWN order NO: %d', $order->novikoIdObjednavkaPart)], Logger::ERROR, $order->id);
						continue;
					}

					try {
						if ($erpOrder->getStatus() === $order->novikoStatus) { // nic se nedeje
							$skipped++;
							$this->logShort(sprintf('SKIPPED - NO CHANGE Order ID %d status "%s" | Noviko ID %s | Noviko status %s', $order->id, $order->status, $order->novikoId, $erpOrder->getStatus()), Logger::INFO, $order->id);

						} else { // stav se v Noviku zmenil
							$order = $this->changeStatus($order, $erpOrder);

							$updated++;
							$this->logShort(sprintf('UPDATED Order ID %d status "%s" -> "%s" | Noviko ID %s | Noviko status %s -> %s', $order->id, $originStatus, $order->status, $order->novikoId, $originNovikoStatus, $order->novikoStatus), Logger::OK, $order->id);
						}

					} catch (Throwable $exception) {
						$errored++;
						$this->message = [$exception->getMessage(), 'error'];
					}
				}
				$this->logShort('########################################');
				$this->logShort([sprintf('ITEMS counter: %d, updated: %d, skipped: %d, errored: %d', $counter, $updated, $skipped, $errored)]);
			} else {
				$this->logShort('No orders for changeStatusToDispatched');
			}

			$this->logProfiler();
			$this->log(['END']);
		} catch (Throwable $e) {
			$this->log([$e->getMessage()], Logger::ERROR);
			Tracy\Debugger::log($e, Tracy\Debugger::ERROR);
		}
	}

	public function getLastMessage(): stdClass
	{
		$msg = new stdClass();
		$msg->text = $this->message[0];
		$msg->type = $this->message[1];

		return $msg;
	}

}
