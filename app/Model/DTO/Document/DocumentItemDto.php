<?php declare(strict_types=1);

namespace App\Model\DTO\Document;

use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Order\Product\ProductItem;

readonly class DocumentItemDto
{

	public int $id;

	public int $extId;

	public int $amount;

	public string $name;

	public function __construct(
		private OrderItem $orderItem,
	)
	{
		if ($orderItem instanceof ProductItem) {
			$this->id = $this->orderItem->id;
			$this->amount = $this->orderItem->amount;
			$this->name = $orderItem->getName();
			$this->extId = $orderItem->variant->extId;
		}
		//TODO - pokud se jedná o balíček, tak rozbít na položky
	}

}
