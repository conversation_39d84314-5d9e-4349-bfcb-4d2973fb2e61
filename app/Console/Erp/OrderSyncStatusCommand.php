<?php declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Erp\ErpOrderService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
	name: 'erp:order:syncStatus',
	description: 'Product import (new/update)',
)]
#[AsCronTask(expression: '5 6-22 * * *', schedule: 'import', transports: 'cronCommands')]
class OrderSyncStatusCommand extends Command
{

	public function __construct(private readonly ErpOrderService $orderService)
	{
		parent::__construct(null);
	}

	protected function configure(): void
	{
		$this->setDescription('Update progress of the orders from <PERSON><PERSON>');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
			$output->writeln('START');
			$this->orderService->updateStatusBulk();
			$output->writeln('DONE');

			return 0; // zero return code means everything is ok
	}

}
